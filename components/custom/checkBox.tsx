import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Checkbox } from "../ui/checkbox";
import { FieldTitle } from "./FieldTitle";
import { LinkRenderer } from "../custom/linkRender";

interface RadioButtonProps {
  register?: any;
  label?: string;
  fieldItem?: any;
  isMandatory?: boolean;
  selectedValue?: boolean;
  handleChange?: any;
  disabled?: boolean;
  errorMessage?: string;
  markdownText?: string;
  compact?: boolean;
}

export function CheckBox(props: RadioButtonProps) {
  const {
    register,
    label,
    fieldItem,
    isMandatory,
    selectedValue,
    handleChange,
    disabled,
    errorMessage,
    markdownText,
    compact = false,
  } = props;


  return (
    <div className={`w-full ${compact ? "mb-1" : "mb-5"} flex ${compact ? "items-center" : "items-start"}`}>
      <Checkbox
        id={`${"checkbox" + label}`}
        defaultChecked={selectedValue}
        onCheckedChange={(type) => handleChange(type)}
        checked={selectedValue}
        name={fieldItem?.name}
        disabled={disabled}
        className={`rounded-[2px] bg-background border ${
          errorMessage ? "border-error" : "border-border"
        } text-background data-[state=checked]:text-background data-[state=checked]:bg-primary p-0 ${
          !compact && "mt-1"
        }`}
      />
      <div className="ml-2">
        {label && (
          <FieldTitle
            label={label}
            htmlFor={`${"checkbox" + label}`}
            isMandatory={!fieldItem?.displayName && isMandatory}
            helpText={fieldItem?.helpText}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        )}
        {markdownText && (
          <ReactMarkdown
            className="markDown mt-1"
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
            components={{ a: LinkRenderer }}
          >
            {markdownText}
          </ReactMarkdown>
        )}
        {errorMessage && (
          <span className="text-sm text-red-500 mt-1">{errorMessage}</span>
        )}
      </div>
    </div>
  );
}
