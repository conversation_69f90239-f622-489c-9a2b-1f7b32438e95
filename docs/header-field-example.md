# Header Field Type Example

This document provides a complete example of how to use the new `header` field type in your form field data.

## Complete Form Example

Here's an example of a form that includes header fields to organize sections:

```json
[
  {
    "fieldName": "personalInfoHeader",
    "text": "Personal Information",
    "type": "header",
    "headerBackground": "#f3f4f6",
    "indexOrder": 1
  },
  {
    "fieldName": "firstName",
    "displayName": "First Name",
    "type": "text",
    "required": true,
    "indexOrder": 2
  },
  {
    "fieldName": "lastName",
    "displayName": "Last Name",
    "type": "text",
    "required": true,
    "indexOrder": 3
  },
  {
    "fieldName": "contactInfoHeader",
    "text": "Contact Information",
    "type": "header",
    "headerBackground": "#dbeafe",
    "indexOrder": 10
  },
  {
    "fieldName": "email",
    "displayName": "Email Address",
    "type": "email",
    "required": true,
    "indexOrder": 11
  },
  {
    "fieldName": "phone",
    "displayName": "Phone Number",
    "type": "mobile",
    "required": true,
    "indexOrder": 12
  },
  {
    "fieldName": "ssnHeader",
    "text": "Social Security Information - Learn More",
    "hyperLinkText": "Learn More",
    "hyperLinkValue": "## Social Security Number Information\n\nA Social Security Number (SSN) is a unique identifier used in the United States. You can find your SSN on:\n\n- Your Social Security card\n- Tax documents (W-2, 1099)\n- Pay stubs\n- Bank statements\n\nIf you don't have an SSN, you may be eligible for an Individual Taxpayer Identification Number (ITIN).",
    "type": "header",
    "headerBackground": "#fef3c7",
    "indexOrder": 20
  },
  {
    "displayName": "Do you have a Social Security Number?",
    "fieldName": "haveSocialSecurityNumber",
    "hyperLinkText": "Social Security Number",
    "hyperLinkValue": "A Social Security Number (SSN) is a unique identifier issued by the United States Social Security Administration. You need an SSN to work, and it's used to determine your eligibility for Social Security benefits and certain government services.",
    "indexOrder": 21,
    "options": [
      "Yes",
      "No"
    ],
    "required": false,
    "type": "radioButton"
  },
  {
    "fieldName": "emergencyContactHeader",
    "text": "Emergency Contact",
    "type": "header",
    "headerBackground": "#fecaca",
    "indexOrder": 30
  },
  {
    "fieldName": "emergencyContactName",
    "displayName": "Emergency Contact Name",
    "type": "text",
    "required": true,
    "indexOrder": 31
  },
  {
    "fieldName": "emergencyContactPhone",
    "displayName": "Emergency Contact Phone",
    "type": "mobile",
    "required": true,
    "indexOrder": 32
  }
]
```

## Visual Result

When rendered, this form will display:

1. **Personal Information** header with light gray background (`#f3f4f6`)
   - First Name field
   - Last Name field

2. **Contact Information** header with light blue background (`#dbeafe`)
   - Email Address field
   - Phone Number field

3. **Social Security Information - Learn More** header with light yellow background (`#fef3c7`)
   - The "Learn More" text will be clickable and show a popup with SSN information
   - Radio button asking about SSN with clickable "Social Security Number" text

4. **Emergency Contact** header with light red background (`#fecaca`)
   - Emergency Contact Name field
   - Emergency Contact Phone field

## Color Recommendations

Here are some recommended background colors for different header types:

### Neutral Headers
- `#f3f4f6` - Light gray (good for general sections)
- `#f9fafb` - Very light gray (subtle dividers)
- `#e5e7eb` - Medium gray (default)

### Information Headers
- `#dbeafe` - Light blue (informational sections)
- `#e0f2fe` - Very light blue (secondary info)

### Warning/Important Headers
- `#fef3c7` - Light yellow (important information)
- `#fecaca` - Light red (urgent/emergency sections)
- `#fed7d7` - Very light red (warnings)

### Success/Completion Headers
- `#dcfce7` - Light green (completed sections)
- `#f0fdf4` - Very light green (success states)

## Best Practices

1. **Use sparingly**: Headers should divide logical sections, not every few fields
2. **Consistent spacing**: Use indexOrder values with gaps (10, 20, 30) to allow for future insertions
3. **Color consistency**: Use similar color families for related sections
4. **Contrast**: Ensure text remains readable on your chosen background colors
5. **Responsive design**: Headers automatically adapt to different screen sizes
6. **Accessibility**: Choose colors that meet WCAG contrast requirements

## Integration with Existing Forms

To add headers to existing forms:

1. Add header field objects to your fieldData array
2. Set appropriate indexOrder values to position headers
3. Choose background colors that match your brand
4. Test on different screen sizes to ensure proper display