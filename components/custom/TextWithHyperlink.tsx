import React, { useState } from "react";
import HyperlinkModal from "./hyperlink-modal";

interface TextWithHyperlinkProps {
  text: string;
  hyperLinkText?: string;
  hyperLinkValue?: string;
  className?: string;
  style?: React.CSSProperties;
}

const TextWithHyperlink: React.FC<TextWithHyperlinkProps> = ({
  text,
  hyperLinkText,
  hyperLinkValue,
  className = "",
  style = {},
}) => {
  const [showModal, setShowModal] = useState(false);

  const renderTextWithHyperlink = () => {
    if (!hyperLinkText || !hyperLinkValue || !text) {
      return <span className={className} style={style}>{text}</span>;
    }

    // Check if hyperLinkText is contained within the text
    if (text.includes(hyperLinkText)) {
      const parts = text.split(hyperLinkText);
      return (
        <span className={className} style={style}>
          {parts[0]}
          <button
            type="button"
            onClick={() => setShowModal(true)}
            className="text-blue-600 hover:text-blue-800 underline cursor-pointer bg-transparent border-none p-0 font-inherit text-inherit"
          >
            {hyperLinkText}
          </button>
          {parts[1]}
        </span>
      );
    }

    // If hyperLinkText is not in the text, append it at the end
    return (
      <span className={className} style={style}>
        {text}{" "}
        <button
          type="button"
          onClick={() => setShowModal(true)}
          className="text-blue-600 hover:text-blue-800 underline cursor-pointer bg-transparent border-none p-0 font-inherit text-inherit"
        >
          {hyperLinkText}
        </button>
      </span>
    );
  };

  return (
    <>
      {renderTextWithHyperlink()}
      {hyperLinkText && hyperLinkValue && (
        <HyperlinkModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          message={hyperLinkValue}
        />
      )}
    </>
  );
};

export default TextWithHyperlink;
