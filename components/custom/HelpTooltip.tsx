import React from "react";
import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface HelpTooltipProps {
  /** The help text content to display in the tooltip */
  helpText: string;
  /** Optional custom className for styling */
  className?: string;
  /** Icon size in pixels */
  iconSize?: number;
  /** Tooltip side positioning */
  side?: "top" | "right" | "bottom" | "left";
  /** Whether to show on click for mobile devices */
  triggerOnClick?: boolean;
}

export const HelpTooltip: React.FC<HelpTooltipProps> = ({
  helpText,
  className,
  iconSize = 18,
  side = "top",
  triggerOnClick = false,
}) => {
  if (!helpText || helpText.trim() === "") {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger
          asChild
          className={cn(
            "cursor-pointer inline-flex items-center justify-center",
            "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm",
            "transition-colors hover:text-primary",
            className
          )}
          aria-label={`Help: ${helpText}`}
          tabIndex={0}
          onKeyDown={(e) => {
            // Allow Enter and Space to trigger tooltip for keyboard users
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              // The tooltip will handle the display
            }
          }}
        >
          <Info
            size={iconSize}
            className="text-muted-foreground hover:text-primary transition-colors"
            aria-hidden="true"
          />
        </TooltipTrigger>
        <TooltipContent
          side={side}
          className={cn(
            "max-w-xs text-sm bg-popover text-popover-foreground",
            "border border-border shadow-md z-50"
          )}
          sideOffset={4}
        >
          <p className="leading-relaxed">{helpText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default HelpTooltip;
