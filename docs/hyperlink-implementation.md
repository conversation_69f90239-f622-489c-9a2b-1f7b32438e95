# Hyperlink Implementation Guide

This document describes how to implement hyperlink functionality in form fields using the new `hyperLinkText` and `hyperLinkValue` properties.

## Overview

The hyperlink functionality allows you to make field labels or text content clickable links that show a popup with additional information when clicked.

## Field Data Structure

To enable hyperlink functionality, add the following properties to your `fieldData`:

```json
{
  "fieldName": "exampleField",
  "displayName": "Regular Label Text Click here for more info",
  "text": "Some descriptive text Click here for more info",
  "hyperLinkText": "Click here for more info",
  "hyperLinkValue": "This is the content that will be shown in the popup modal when the user clicks the hyperlink.",
  "type": "text"
}
```

## Properties

### hyperLinkText

- **Type**: `string`
- **Description**: The text within the displayName/text that will become a clickable link. This text must be present within the displayName or text field.
- **Required**: Only if you want hyperlink functionality

### hyperLinkValue

- **Type**: `string`
- **Description**: The content that will be displayed in the popup modal when the link is clicked
- **Supports**: Plain text and Markdown formatting
- **Required**: Only if you want hyperlink functionality

## Usage Examples

### Example 1: Text Field with Hyperlink

```json
{
  "fieldName": "termsAndConditions",
  "displayName": "I agree to the View Terms & Conditions",
  "hyperLinkText": "View Terms & Conditions",
  "hyperLinkValue": "## Terms and Conditions\n\nBy using this service, you agree to...\n\n- Point 1\n- Point 2\n- Point 3",
  "type": "checkbox",
  "required": true
}
```

### Example 2: Display Information with Hyperlink

```json
{
  "fieldName": "privacyNotice",
  "text": "Please review our Privacy Policy",
  "hyperLinkText": "Privacy Policy",
  "hyperLinkValue": "Your privacy is important to us. We collect and use your information to provide better services...",
  "type": "displayInformation"
}
```

### Example 3: Input Field with Help Link

```json
{
  "fieldName": "socialSecurityNumber",
  "displayName": "Social Security Number (Where to find your SSN)",
  "hyperLinkText": "Where to find your SSN",
  "hyperLinkValue": "Your Social Security Number can be found on:\n\n- Your Social Security card\n- Tax documents (W-2, 1099)\n- Pay stubs\n- Bank statements",
  "type": "text",
  "required": true,
  "placeholder": "XXX-XX-XXXX"
}
```

## Implementation Details

### Components Modified

1. **FieldTitle.tsx**: Enhanced to render hyperlinks in field labels
2. **DynamicFields.tsx**: Updated to pass hyperlink properties and handle text hyperlinks
3. **TextWithHyperlink.tsx**: New component for rendering text with embedded hyperlinks
4. **HyperlinkModal.tsx**: New modal component for displaying hyperlink content

### How It Works

1. When `hyperLinkText` and `hyperLinkValue` are present in the field data:

   - The component searches for `hyperLinkText` within the `displayName` or `text` field
   - If found, that portion becomes a clickable link while the rest remains as normal text
   - If not found, the `hyperLinkText` is appended to the end as a clickable link
   - Clicking the link opens a modal with the `hyperLinkValue` content
   - The modal supports Markdown formatting for rich content

2. If either property is missing, the field behaves normally without hyperlink functionality

### Styling

The hyperlinks use the following CSS classes:

- `text-blue-600 hover:text-blue-800 underline cursor-pointer`
- The modal follows the existing design system with primary color accents

### Accessibility

- Links are keyboard accessible (can be tabbed to and activated with Enter/Space)
- Modal can be closed with Escape key or by clicking outside
- Proper semantic markup with `button` elements for screen readers

## Backward Compatibility

This implementation is fully backward compatible. Existing forms without `hyperLinkText` and `hyperLinkValue` will continue to work exactly as before.

## Testing

To test the hyperlink functionality:

1. Add the hyperlink properties to your field data
2. Ensure the `hyperLinkText` is included within the `displayName` or `text` field
3. Render the form
4. Verify that the specified text portion appears as a clickable blue link
5. Verify that the rest of the displayName/text appears as normal text
6. Click the link and verify the modal opens with the correct content
7. Test closing the modal with the close button, clicking outside, or pressing Escape

## Notes

- The hyperlink modal content supports full Markdown syntax including headers, lists, links, etc.
- Multiple hyperlinks can be used on the same form
- The modal is responsive and works on all screen sizes
- Content in the modal is scrollable if it exceeds the modal height
- If `hyperLinkText` is not found within the `displayName` or `text`, it will be appended at the end
- The hyperlink text inherits the styling from its parent element
- For your example: "Do you have a Social Security Number?" with `hyperLinkText: "Social Security Number"` will make "Social Security Number" clickable within that sentence

## Header Field Type

### Overview

The `header` field type creates a full-width header section with customizable background color that spans the entire form container width.

### Field Data Structure

```json
{
  "fieldName": "sectionHeader",
  "text": "Personal Information",
  "type": "header",
  "headerBackground": "#f3f4f6"
}
```

### Properties

#### headerBackground

- **Type**: `string`
- **Description**: CSS color value for the header background (hex, rgb, rgba, or named colors)
- **Default**: `#e5e7eb` (light gray)
- **Example**: `#3b82f6`, `rgb(59, 130, 246)`, `rgba(59, 130, 246, 0.1)`

### Header Usage Examples

#### Example 1: Basic Header

```json
{
  "fieldName": "personalInfoHeader",
  "text": "Personal Information",
  "type": "header",
  "headerBackground": "#f3f4f6"
}
```

#### Example 2: Header with Hyperlink

```json
{
  "fieldName": "educationHeader",
  "text": "Education Background - View Requirements",
  "hyperLinkText": "View Requirements",
  "hyperLinkValue": "## Education Requirements\n\n- High school diploma or equivalent\n- Official transcripts required\n- International students need credential evaluation",
  "type": "header",
  "headerBackground": "#dbeafe"
}
```

#### Example 3: Section Divider Header

```json
{
  "fieldName": "emergencyContactHeader",
  "text": "Emergency Contact Information",
  "type": "header",
  "headerBackground": "#fef3c7"
}
```

### Header Styling

- Headers are displayed with bold text (`font-bold text-lg`)
- Text color is set to `text-gray-800` for good contrast
- Headers extend to the full width of the form container
- Background spans edge-to-edge within the form boundary
- Responsive padding matches the form container padding
- Headers include margin bottom for proper spacing

### Implementation Notes

- Headers work within existing form containers without breaking layout
- Multiple headers can be used to create form sections
- Headers support hyperlink functionality like other field types
- Headers are responsive and adapt to different screen sizes
