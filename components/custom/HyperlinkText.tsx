import React, { useState } from "react";
import HyperlinkModal from "./hyperlink-modal";

interface HyperlinkTextProps {
  text: string;
  hyperLinkText?: string;
  hyperLinkValue?: string;
  className?: string;
  style?: React.CSSProperties;
}

const HyperlinkText: React.FC<HyperlinkTextProps> = ({
  text,
  hyperLinkText,
  hyperLinkValue,
  className = "",
  style = {},
}) => {
  const [showModal, setShowModal] = useState(false);

  if (hyperLinkText && hyperLinkValue && text) {
    return (
      <>
        <span className={className} style={style}>
          {text}{" "}
          <button
            type="button"
            onClick={() => setShowModal(true)}
            className="text-blue-600 hover:text-blue-800 underline cursor-pointer bg-transparent border-none p-0 font-inherit text-inherit"
          >
            {hyperLinkText}
          </button>
        </span>
        <HyperlinkModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          message={hyperLinkValue}
        />
      </>
    );
  }

  return (
    <span className={className} style={style}>
      {text}
    </span>
  );
};

export default HyperlinkText;
