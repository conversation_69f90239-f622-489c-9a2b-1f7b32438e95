import { Label } from "../ui/label";
import { useAtom } from "jotai";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";
import HelpTooltip from "./HelpTooltip";
import TextWithHyperlink from "./TextWithHyperlink";
import ReactMarkdown from "react-markdown";
import { LinkRenderer } from "./linkRender";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Span } from "next/dist/trace";

interface TitleProps {
  label: string | undefined;
  isMandatory?: boolean;
  htmlFor?: string;
  isPreview?: boolean;
  helpText?: string;
  hyperLinkText?: string;
  hyperLinkValue?: string;
}

export const FieldTitle = ({
  label,
  isMandatory,
  htmlFor,
  isPreview,
  helpText,
  hyperLinkText,
  hyperLinkValue,
}: TitleProps) => {
  const [fontSize] = useAtom(fontSizeAtom);

  // Check if label contains markdown-style links [text](url)
  const hasMarkdownLinks = label && /\[.*?\]\(.*?\)/.test(label);

  const renderLabel = () => {
    if (hasMarkdownLinks) {
      return (
        <div className="inline">
          <ReactMarkdown
            className="markDown inline"
            components={{
              a: LinkRenderer,
              p: ({ node, ...props }) => <span {...props} />,
            }}
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
          >
            {label || ""}
          </ReactMarkdown>
        </div>
      );
    } else if (hyperLinkText && hyperLinkValue) {
      return (
        <TextWithHyperlink
          text={label || ""}
          hyperLinkText={hyperLinkText}
          hyperLinkValue={hyperLinkValue}
        />
      );
    } else {
      return <span>{label || ""}</span>;
    }
  };

  if (isPreview) {
    return (
      <div className="mb-1">
        <div className="flex items-center gap-2">
          <Label htmlFor={htmlFor} className="text-xl font-bold">
            {renderLabel()}
          </Label>
          <div className="flex-1">
            {helpText && <HelpTooltip helpText={helpText} iconSize={18} />}
          </div>
        </div>
      </div>
    );
  } else
    return (
      <div className="mb-1">
        <div className="flex items-center gap-2">
          <Label
            htmlFor={htmlFor}
            className="text-sm"
            style={getBrandSpecificFontStyle(fontSize, "label")}
          >
            {renderLabel()}
            <span className="px-1">{isMandatory && <Label className="text-sm text-red-500">*</Label>}</span>
          </Label>
          <div className="flex-1">
            {helpText && <HelpTooltip helpText={helpText} iconSize={18} />}
          </div>
        </div>
      </div>
    );
};
