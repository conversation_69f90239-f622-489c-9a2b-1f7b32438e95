import React from "react";
import {
  <PERSON>ertDialog,
  AlertDialog<PERSON>ction,
  AlertDialogContent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Info } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { LinkRenderer } from "./linkRender";

interface HyperlinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  buttonText?: string;
}

const HyperlinkModal: React.FC<HyperlinkModalProps> = ({
  isOpen,
  onClose,
  title = "Information",
  message,
  buttonText = "Close",
}) => {
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="max-w-md border-0 shadow-lg">
        <div className="bg-primary h-2 w-full absolute top-0 left-0 rounded-t-lg"></div>
        <AlertDialogHeader className="flex flex-col items-center pt-6">
          <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
            <Info className="h-8 w-8 text-primary" />
          </div>
          <AlertDialogTitle className="text-xl font-semibold text-center text-gray-800">
            {title}
          </AlertDialogTitle>
          <div className="mt-2 text-center text-gray-600 max-h-96 overflow-y-auto">
            <ReactMarkdown
              className="markDown"
              components={{ a: LinkRenderer }}
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {message}
            </ReactMarkdown>
          </div>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex justify-center sm:justify-center mt-4">
          <AlertDialogAction
            onClick={() => onClose()}
            className="bg-primary hover:bg-primary/90 text-white font-medium px-8"
          >
            {buttonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default HyperlinkModal;
