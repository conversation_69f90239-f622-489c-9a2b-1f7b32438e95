# Help Tooltip Component

The `HelpTooltip` component provides a reusable way to display help text for form fields in the OAP frontend application. It shows an information icon that displays help text in a tooltip when hovered or focused.

## Features

- **Accessible**: Includes proper ARIA labels and keyboard navigation support
- **Responsive**: Works on both desktop and mobile devices
- **Customizable**: Configurable icon size, tooltip positioning, and styling
- **Integrated**: Automatically integrates with existing form field components
- **Consistent**: Follows the existing design system and styling patterns

## Usage

### Automatic Integration with Form Fields

The tooltip automatically appears for any form field that has a `helpText` property in its field configuration:

```typescript
// Field configuration example
const fieldConfig = {
  fieldName: "email",
  displayName: "Email Address",
  type: "text",
  helpText: "Enter your primary email address for account communications",
  // ... other field properties
};
```

### Manual Usage

You can also use the `HelpTooltip` component directly:

```tsx
import HelpTooltip from '@/components/custom/HelpTooltip';

// Basic usage
<HelpTooltip helpText="This is helpful information" />

// With custom icon size
<HelpTooltip 
  helpText="This is helpful information" 
  iconSize={20} 
/>

// With custom positioning
<HelpTooltip 
  helpText="This is helpful information" 
  side="right" 
/>
```

### With FieldTitle Component

```tsx
import { FieldTitle } from '@/components/custom/FieldTitle';

<FieldTitle 
  label="Password" 
  isMandatory={true} 
  helpText="Password must be at least 8 characters with uppercase, lowercase, number, and special character"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `helpText` | `string` | - | **Required.** The help text to display in the tooltip |
| `className` | `string` | - | Optional custom CSS classes |
| `iconSize` | `number` | `16` | Size of the info icon in pixels |
| `side` | `"top" \| "right" \| "bottom" \| "left"` | `"top"` | Tooltip positioning |
| `triggerOnClick` | `boolean` | `false` | Whether to show tooltip on click (useful for mobile) |

## Accessibility

The component includes several accessibility features:

- **ARIA Labels**: The trigger button includes an `aria-label` with the help text
- **Keyboard Navigation**: Supports Tab navigation and Enter/Space key activation
- **Focus Management**: Proper focus indicators and ring styling
- **Screen Reader Support**: Compatible with screen readers

## Styling

The component uses Tailwind CSS classes and follows the existing design system:

- **Icon**: Uses `lucide-react` Info icon with muted foreground color
- **Tooltip**: Styled with popover background and border
- **Hover Effects**: Smooth color transitions on hover
- **Focus States**: Clear focus rings for keyboard navigation

## Integration with Field Types

The tooltip is automatically integrated with all form field types:

- Text inputs
- Dropdowns/Select fields
- Checkboxes and radio buttons
- Textareas
- Date pickers
- File uploads
- Tag inputs
- Password fields
- Mobile number inputs

## Examples

### Basic Field with Help Text

```tsx
// This will automatically show a help tooltip
const fieldConfig = {
  fieldName: "phoneNumber",
  displayName: "Phone Number",
  type: "text",
  helpText: "Include country code for international numbers",
  isMandatory: false
};
```

### Long Help Text

The tooltip automatically handles long text with proper wrapping:

```tsx
const fieldConfig = {
  fieldName: "password",
  displayName: "Password",
  type: "password",
  helpText: "Your password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character. Avoid using common words or personal information.",
  isMandatory: true
};
```

### Field Without Help Text

Fields without `helpText` will not show the tooltip icon:

```tsx
const fieldConfig = {
  fieldName: "firstName",
  displayName: "First Name",
  type: "text",
  isMandatory: true
  // No helpText = no tooltip
};
```

## Browser Support

The component works in all modern browsers and is compatible with:

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Migration from TagInput

The `TagInput` component previously displayed help text as static text below the input. This has been updated to use the new tooltip system for consistency. The `helpTitle` and `helpText` props have been removed from `TagInput` and should now be handled through the field configuration.

## Testing

The component includes comprehensive tests covering:

- Rendering with and without help text
- Accessibility attributes
- Keyboard interaction
- Custom props
- Integration with FieldTitle

Run tests with:
```bash
npm test -- HelpTooltip.test.tsx
```
